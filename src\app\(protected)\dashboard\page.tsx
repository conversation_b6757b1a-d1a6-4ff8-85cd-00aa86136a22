"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Heart } from "lucide-react";
import DashboardLayout from "@/components/dashboard/dashboard-layout";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Heart className="h-8 w-8 text-pink-500 mx-auto mb-4 animate-pulse" />
          <p>Memuat...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return <DashboardLayout user={session.user} />;
}
